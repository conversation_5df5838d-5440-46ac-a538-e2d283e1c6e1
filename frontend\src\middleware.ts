import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // Admin sayfaları için koruma
  if (pathname.startsWith('/admin')) {
    // Login ve forgot-password sayfalarına erişime izin ver
    if (pathname === '/admin/login' || pathname === '/admin/forgot-password') {
      return NextResponse.next()
    }

    // Admin token kontrolü
    const adminToken = request.cookies.get('admin_token')

    console.log('Middleware - pathname:', pathname, 'adminToken:', adminToken?.value)

    if (!adminToken || adminToken.value !== 'admin_authenticated_token') {
      // Admin token yoksa veya geçersizse login sayfasına yönlendir
      console.log('Admin access denied, redirecting to login')
      return NextResponse.redirect(new URL('/admin/login', request.url))
    }
  }

  return NextResponse.next()
}

export const config = {
  matcher: [
    /*
     * Admin sayfaları için matcher
     * - /admin (ana sayfa)
     * - /admin/... (tüm alt sayfalar)
     * Ancak /admin/login ve /admin/forgot-password hariç
     */
    '/admin/((?!login|forgot-password).*)',
    '/admin'
  ]
}
