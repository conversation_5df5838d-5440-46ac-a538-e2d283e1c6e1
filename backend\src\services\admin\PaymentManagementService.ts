// RFC-501: Payment Management Service Implementation
import { PrismaClient, EscrowStatus, NotificationType, Prisma } from '@prisma/client';
import { createClient, RedisClientType } from 'redis';

export interface BankTransferReceipt {
  id: string;
  paymentId: string;
  receiptUrl: string;
  uploadedAt: Date;
  status: 'pending' | 'verified' | 'rejected';
  verifiedBy?: string;
  verifiedAt?: Date;
  rejectionReason?: string;
  bankDetails: {
    senderName: string;
    senderAccount: string;
    receiverAccount: string;
    amount: number;
    transferDate: Date;
    referenceNumber: string;
  };
}

export interface EscrowAccount {
  id: string;
  paymentId: string;
  orderId: string;
  amount: number;
  status: EscrowStatus;
  createdAt: Date;
  releasedAt?: Date;
  refundedAt?: Date;
  holdReason?: string;
  customer: {
    id: string;
    email: string;
    companyName?: string;
  };
  producer: {
    id: string;
    email: string;
    companyName?: string;
  };
}

export interface CommissionReport {
  date: string;
  totalOrders: number;
  totalVolume: number; // m² or tons
  commissionAmount: number;
  commissionRate: number;
  breakdown: {
    dimensionalProducts: {
      volume: number; // m²
      commission: number; // $1 per m²
    };
    blockProducts: {
      volume: number; // tons
      commission: number; // $10 per ton
    };
  };
}

export interface PaymentAnalytics {
  totalRevenue: number;
  pendingPayments: number;
  escrowBalance: number;
  commissionEarned: number;
  refundsProcessed: number;
  paymentsByMethod: { [key: string]: number };
  revenueByRegion: { [key: string]: number };
  commissionTrend: { date: string; commission: number }[];
  fraudAlerts: FraudAlert[];
}

export interface FraudAlert {
  id: string;
  paymentId: string;
  type: 'suspicious_amount' | 'unusual_pattern' | 'high_risk_user' | 'duplicate_transaction';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  createdAt: Date;
  investigated: boolean;
}

export class PaymentManagementService {
  private prisma: PrismaClient;
  private redis: RedisClientType;

  constructor() {
    this.prisma = new PrismaClient();
    this.redis = createClient({
      url: process.env.REDIS_URL || 'redis://localhost:6379'
    });
    
    this.redis.on('error', (err) => {
      console.error('Redis Client Error:', err);
    });
  }

  async connect(): Promise<void> {
    if (!this.redis.isOpen) {
      await this.redis.connect();
    }
  }

  async disconnect(): Promise<void> {
    if (this.redis.isOpen) {
      await this.redis.disconnect();
    }
    await this.prisma.$disconnect();
  }

  // Get pending bank transfer verifications
  async getPendingBankTransferVerifications(): Promise<BankTransferReceipt[]> {
    await this.connect();
    
    // Check cache first
    const cached = await this.redis.get('pending_bank_transfers');
    if (cached) {
      return JSON.parse(cached);
    }

    // Get payments with pending bank transfer receipts
    const pendingPayments = await this.prisma.payment.findMany({
      where: {
        paymentMethod: 'BANK_TRANSFER',
        status: 'PENDING',
        bankTransferReceipt: { not: Prisma.JsonNull }
      },
      include: {
        user: {
          include: {
            profile: true
          }
        },
        order: true
      },
      orderBy: { createdAt: 'asc' }
    });

    const receipts: BankTransferReceipt[] = pendingPayments.map(payment => {
      const receiptData = payment.bankTransferReceipt as any;
      return {
        id: `receipt_${payment.id}`,
        paymentId: payment.id,
        receiptUrl: receiptData?.receiptUrl || '',
        uploadedAt: receiptData?.uploadedAt ? new Date(receiptData.uploadedAt) : payment.createdAt,
        status: 'pending' as const,
        bankDetails: {
          senderName: payment.user?.profile?.companyName || payment.user?.email || 'Unknown',
          senderAccount: receiptData?.senderAccount || '',
          receiverAccount: process.env.COMPANY_BANK_ACCOUNT || '',
          amount: Number(payment.amount),
          transferDate: receiptData?.transferDate ? new Date(receiptData.transferDate) : payment.createdAt,
          referenceNumber: receiptData?.referenceNumber || payment.id
        }
      };
    });

    // Cache for 5 minutes
    await this.redis.setEx('pending_bank_transfers', 300, JSON.stringify(receipts));

    return receipts;
  }

  // Verify bank transfer receipt
  async verifyBankTransferReceipt(receiptId: string, adminId: string, verified: boolean, reason?: string): Promise<void> {
    await this.connect();
    
    const paymentId = receiptId.replace('receipt_', '');
    
    const payment = await this.prisma.payment.findUnique({
      where: { id: paymentId },
      include: { user: true, order: true }
    });

    if (!payment) {
      throw new Error('Payment not found');
    }

    if (verified) {
      // Approve payment
      await this.prisma.payment.update({
        where: { id: paymentId },
        data: {
          status: 'COMPLETED',
          verifiedAt: new Date(),
          verifiedBy: adminId
        }
      });

      // Create escrow if this is a down payment
      if (payment.paymentType === 'DOWN_PAYMENT') {
        await this.prisma.payment.update({
          where: { id: paymentId },
          data: { escrowStatus: 'HELD' }
        });
      }

      // Send approval notification
      await this.prisma.notification.create({
        data: {
          userId: payment.userId,
          title: 'Payment Verified',
          message: `Your bank transfer payment of $${payment.amount} has been verified and processed.`,
          notificationType: NotificationType.PAYMENT_RECEIVED
        }
      });
    } else {
      // Reject payment
      await this.prisma.payment.update({
        where: { id: paymentId },
        data: {
          status: 'FAILED',
          verifiedAt: new Date(),
          verifiedBy: adminId,
          failureReason: reason
        }
      });

      // Send rejection notification
      await this.prisma.notification.create({
        data: {
          userId: payment.userId,
          title: 'Payment Rejected',
          message: `Your bank transfer payment has been rejected. Reason: ${reason}`,
          notificationType: NotificationType.SYSTEM_ANNOUNCEMENT
        }
      });
    }

    // Log the action
    await this.prisma.auditLog.create({
      data: {
        userId: adminId,
        action: verified ? 'VERIFY_BANK_TRANSFER' : 'REJECT_BANK_TRANSFER',
        resource: 'PAYMENT',
        resourceId: paymentId,
        newValues: { verified, reason, amount: Number(payment.amount) }
      }
    });

    // Clear cache
    await this.redis.del('pending_bank_transfers');
  }

  // Get active escrow accounts
  async getActiveEscrowAccounts(): Promise<EscrowAccount[]> {
    await this.connect();
    
    const escrowPayments = await this.prisma.payment.findMany({
      where: {
        escrowStatus: 'HELD'
      },
      include: {
        user: {
          include: { profile: true }
        },
        order: {
          include: {
            producer: {
              include: { profile: true }
            }
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    });

    return escrowPayments.map(payment => ({
      id: payment.id,
      paymentId: payment.id,
      orderId: payment.orderId || '',
      amount: Number(payment.amount),
      status: payment.escrowStatus || 'PENDING',
      createdAt: payment.createdAt,
      releasedAt: payment.escrowReleasedAt || undefined,
      refundedAt: payment.escrowRefundedAt || undefined,
      holdReason: payment.escrowHoldReason || undefined,
      customer: {
        id: payment.user.id,
        email: payment.user.email,
        companyName: payment.user.profile?.companyName
      },
      producer: {
        id: payment.order?.producer.id || '',
        email: payment.order?.producer.email || '',
        companyName: payment.order?.producer.profile?.companyName
      }
    }));
  }

  // Release escrow payment
  async releaseEscrow(escrowId: string, adminId: string, reason?: string): Promise<void> {
    await this.connect();
    
    const payment = await this.prisma.payment.update({
      where: { id: escrowId },
      data: {
        escrowStatus: 'RELEASED',
        escrowReleasedAt: new Date(),
        escrowReleasedBy: adminId
      },
      include: {
        user: true,
        order: {
          include: { producer: true }
        }
      }
    });

    // Calculate and record commission
    await this.calculateAndRecordCommission(payment);

    // Send notifications
    const notifications = [
      {
        userId: payment.userId,
        title: 'Escrow Payment Released',
        message: `Your escrow payment of $${payment.amount} has been released to the producer.`,
        notificationType: NotificationType.PAYMENT_RECEIVED
      }
    ];

    if (payment.order?.producerId) {
      notifications.push({
        userId: payment.order.producerId,
        title: 'Payment Received',
        message: `You have received a payment of $${payment.amount} from escrow.`,
        notificationType: NotificationType.PAYMENT_RECEIVED
      });
    }

    await this.prisma.notification.createMany({ data: notifications });

    // Log the action
    await this.prisma.auditLog.create({
      data: {
        userId: adminId,
        action: 'RELEASE_ESCROW',
        resource: 'PAYMENT',
        resourceId: escrowId,
        newValues: { reason, amount: Number(payment.amount) }
      }
    });
  }

  // Calculate commission based on PRD requirements
  private async calculateAndRecordCommission(payment: any): Promise<void> {
    if (!payment.order) return;

    const orderItems = await this.prisma.orderItem.findMany({
      where: { orderId: payment.orderId },
      include: { product: true }
    });

    let totalCommission = 0;

    for (const item of orderItems) {
      const product = item.product;
      
      // Get category from categoryId - we'll need to fetch category details
      // For now, use a simple check based on product name or specifications
      const isBlock = product.name.toLowerCase().includes('block') ||
                     JSON.stringify(product.specifications).toLowerCase().includes('block');

      if (isBlock) {
        // $10 per ton for blocks
        const tons = item.quantity; // Assuming quantity is in tons for blocks
        totalCommission += tons * 10;
      } else {
        // $1 per m² for dimensional products
        const squareMeters = item.quantity; // Assuming quantity is in m² for dimensional
        totalCommission += squareMeters * 1;
      }
    }

    // Record commission
    await this.prisma.commission.create({
      data: {
        paymentId: payment.id,
        orderId: payment.orderId,
        amount: totalCommission,
        rate: totalCommission / payment.amount,
        calculatedAt: new Date()
      }
    });
  }

  // Get commission tracking data
  async getCommissionTracking(): Promise<{
    dailyCommissions: CommissionReport;
    monthlyCommissions: CommissionReport;
    totalPlatformEarnings: number;
    commissionTrend: { date: string; commission: number }[];
  }> {
    await this.connect();
    
    const now = new Date();
    const startOfDay = new Date(now.setHours(0, 0, 0, 0));
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

    const [
      dailyCommissions,
      monthlyCommissions,
      totalCommissions,
      commissionTrend
    ] = await Promise.all([
      this.getCommissionReport(startOfDay, new Date()),
      this.getCommissionReport(startOfMonth, new Date()),
      this.prisma.commission.aggregate({
        _sum: { amount: true }
      }),
      this.getCommissionTrend(30)
    ]);

    return {
      dailyCommissions,
      monthlyCommissions,
      totalPlatformEarnings: Number(totalCommissions._sum.amount || 0),
      commissionTrend
    };
  }

  // Get commission report for a date range
  private async getCommissionReport(startDate: Date, endDate: Date): Promise<CommissionReport> {
    const commissions = await this.prisma.commission.findMany({
      where: {
        calculatedAt: {
          gte: startDate,
          lte: endDate
        }
      },
      include: {
        order: {
          include: {
            orderItems: {
              include: { product: true }
            }
          }
        }
      }
    });

    let totalCommission = 0;
    let totalOrders = commissions.length;
    let dimensionalVolume = 0;
    let dimensionalCommission = 0;
    let blockVolume = 0;
    let blockCommission = 0;

    for (const commission of commissions) {
      totalCommission += commission.amount;
      
      if (commission.order) {
        for (const item of commission.order.orderItems) {
          const isBlock = item.product.name.toLowerCase().includes('block') ||
                         JSON.stringify(item.product.specifications).toLowerCase().includes('block');

          if (isBlock) {
            blockVolume += item.quantity;
            blockCommission += item.quantity * 10;
          } else {
            dimensionalVolume += item.quantity;
            dimensionalCommission += item.quantity * 1;
          }
        }
      }
    }

    const totalVolume = dimensionalVolume + blockVolume;
    const commissionRate = totalVolume > 0 ? totalCommission / totalVolume : 0;

    return {
      date: startDate.toISOString().split('T')[0],
      totalOrders,
      totalVolume,
      commissionAmount: totalCommission,
      commissionRate,
      breakdown: {
        dimensionalProducts: {
          volume: dimensionalVolume,
          commission: dimensionalCommission
        },
        blockProducts: {
          volume: blockVolume,
          commission: blockCommission
        }
      }
    };
  }

  // Get commission trend
  private async getCommissionTrend(days: number): Promise<{ date: string; commission: number }[]> {
    const trend = [];
    const now = new Date();

    for (let i = days - 1; i >= 0; i--) {
      const date = new Date(now);
      date.setDate(date.getDate() - i);
      const startOfDay = new Date(date.setHours(0, 0, 0, 0));
      const endOfDay = new Date(date.setHours(23, 59, 59, 999));

      const commission = await this.prisma.commission.aggregate({
        _sum: { amount: true },
        where: {
          calculatedAt: {
            gte: startOfDay,
            lte: endOfDay
          }
        }
      });

      trend.push({
        date: startOfDay.toISOString().split('T')[0],
        commission: Number(commission._sum.amount || 0)
      });
    }

    return trend;
  }

  // Get payment analytics
  async getPaymentAnalytics(): Promise<PaymentAnalytics> {
    await this.connect();
    
    // Check cache first
    const cached = await this.redis.get('payment_analytics');
    if (cached) {
      return JSON.parse(cached);
    }

    const [
      totalRevenue,
      pendingPayments,
      escrowBalance,
      commissionEarned,
      refundsProcessed,
      paymentsByMethod,
      fraudAlerts
    ] = await Promise.all([
      this.prisma.payment.aggregate({
        _sum: { amount: true },
        where: { status: 'COMPLETED' }
      }),
      this.prisma.payment.count({
        where: { status: 'PENDING' }
      }),
      this.prisma.payment.aggregate({
        _sum: { amount: true },
        where: { escrowStatus: 'HELD' }
      }),
      this.prisma.commission.aggregate({
        _sum: { amount: true }
      }),
      this.prisma.payment.count({
        where: { status: 'REFUNDED' }
      }),
      this.prisma.payment.groupBy({
        by: ['paymentMethod'],
        _count: true
      }),
      this.getFraudAlerts()
    ]);

    const analytics: PaymentAnalytics = {
      totalRevenue: Number(totalRevenue._sum.amount || 0),
      pendingPayments,
      escrowBalance: Number(escrowBalance._sum.amount || 0),
      commissionEarned: Number(commissionEarned._sum.amount || 0),
      refundsProcessed,
      paymentsByMethod: paymentsByMethod.reduce((acc, item) => {
        acc[item.paymentMethod] = item._count;
        return acc;
      }, {} as { [key: string]: number }),
      revenueByRegion: {}, // Would be calculated from user profiles
      commissionTrend: await this.getCommissionTrend(30),
      fraudAlerts
    };

    // Cache for 5 minutes
    await this.redis.setEx('payment_analytics', 300, JSON.stringify(analytics));

    return analytics;
  }

  // Get fraud alerts
  private async getFraudAlerts(): Promise<FraudAlert[]> {
    // This would implement actual fraud detection logic
    // For now, return mock data
    return [];
  }
}
