import { Router, Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import { authMiddleware, AuthenticatedRequest } from '../../middleware/authMiddleware';
import { asyncHandler } from '../../middleware/errorHandler';
import { z } from 'zod';

const router = Router();
const prisma = new PrismaClient();

// Admin middleware - check if user is admin
const adminMiddleware = (req: any, res: any, next: any) => {
  if (!req.user || req.user.userType !== 'ADMIN') {
    return res.status(403).json({
      success: false,
      error: {
        message: 'Admin access required',
        statusCode: 403
      }
    });
  }
  next();
};

// Apply auth and admin middleware to all routes
router.use(authMiddleware);
router.use(adminMiddleware);

/**
 * @swagger
 * /api/admin/dashboard:
 *   get:
 *     summary: Get admin dashboard statistics
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Dashboard stats retrieved successfully
 */
router.get('/dashboard', authMiddleware, asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const [
    totalUsers,
    totalProducers,
    totalCustomers,
    totalProducts,
    pendingProducts,
    totalOrders,
    totalRevenue,
    monthlyRevenue,
    pendingQuotes,
    recentActivity
  ] = await Promise.all([
    prisma.user.count(),
    prisma.user.count({ where: { userType: 'producer' } }),
    prisma.user.count({ where: { userType: 'customer' } }),
    prisma.product.count({ where: { isActive: true } }),
    prisma.product.count({ where: { status: 'PENDING' } }),
    prisma.order.count(),
    prisma.order.aggregate({
      where: { status: 'DELIVERED' },
      _sum: { totalAmount: true }
    }),
    prisma.order.aggregate({
      where: {
        status: 'DELIVERED',
        createdAt: {
          gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1)
        }
      },
      _sum: { totalAmount: true }
    }),
    prisma.quote.count({ where: { status: 'PENDING' } }),
    prisma.order.findMany({
      take: 10,
      orderBy: { createdAt: 'desc' },
      include: {
        customer: {
          select: { firstName: true, lastName: true }
        },
        quote: {
          include: {
            product: {
              select: { name: true }
            }
          }
        }
      }
    })
  ]);

  res.json({
    success: true,
    data: {
      users: {
        total: totalUsers,
        producers: totalProducers,
        customers: totalCustomers
      },
      products: {
        total: totalProducts,
        pending: pendingProducts
      },
      orders: {
        total: totalOrders,
        totalRevenue: totalRevenue._sum.totalAmount || 0,
        monthlyRevenue: monthlyRevenue._sum.totalAmount || 0
      },
      quotes: {
        pending: pendingQuotes
      },
      recentActivity
    }
  });
}));

/**
 * @swagger
 * /api/admin/products/pending:
 *   get:
 *     summary: Get pending products for approval
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Pending products retrieved successfully
 */
router.get('/products/pending', authMiddleware, asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 20;
  const skip = (page - 1) * limit;

  const [products, total] = await Promise.all([
    prisma.product.findMany({
      where: { status: 'PENDING' },
      skip,
      take: limit,
      orderBy: { createdAt: 'desc' },
      include: {
        producer: {
          select: {
            id: true,
            companyName: true,
            profile: {
              select: {
                city: true,
                country: true
              }
            }
          }
        },
        images: {
          take: 3
        }
      }
    }),
    prisma.product.count({ where: { status: 'PENDING' } })
  ]);

  const totalPages = Math.ceil(total / limit);

  res.json({
    success: true,
    data: {
      products,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    }
  });
}));

/**
 * @swagger
 * /api/admin/products/{id}/approve:
 *   put:
 *     summary: Approve product
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Product approved successfully
 */
router.put('/products/:id/approve', authMiddleware, asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;
  const { notes } = req.body;

  const product = await prisma.product.findUnique({
    where: { id },
    include: {
      producer: true
    }
  });

  if (!product) {
    return res.status(404).json({
      success: false,
      error: {
        message: 'Product not found',
        statusCode: 404
      }
    });
  }

  const updatedProduct = await prisma.product.update({
    where: { id },
    data: {
      status: 'APPROVED',
      isActive: true
    }
  });

  // Send notification using notification service
  const notificationService = req.app.get('notificationService');
  if (notificationService) {
    await notificationService.notifyProductApproval(
      product.id,
      product.producerId,
      true,
      notes
    );
  }

  res.json({
    success: true,
    data: updatedProduct,
    message: 'Product approved successfully'
  });
}));

/**
 * @swagger
 * /api/admin/products/{id}/reject:
 *   put:
 *     summary: Reject product
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               reason:
 *                 type: string
 *     responses:
 *       200:
 *         description: Product rejected successfully
 */
router.put('/products/:id/reject', authMiddleware, asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;
  const { reason } = req.body;

  if (!reason) {
    return res.status(400).json({
      success: false,
      error: {
        message: 'Rejection reason is required',
        statusCode: 400
      }
    });
  }

  const product = await prisma.product.findUnique({
    where: { id },
    include: {
      producer: true
    }
  });

  if (!product) {
    return res.status(404).json({
      success: false,
      error: {
        message: 'Product not found',
        statusCode: 404
      }
    });
  }

  const updatedProduct = await prisma.product.update({
    where: { id },
    data: {
      status: 'REJECTED',
      isActive: false
    }
  });

  // Send notification using notification service
  const notificationService = req.app.get('notificationService');
  if (notificationService) {
    await notificationService.notifyProductApproval(
      product.id,
      product.producerId,
      false,
      reason
    );
  }

  res.json({
    success: true,
    data: updatedProduct,
    message: 'Product rejected successfully'
  });
}));

export default router;
