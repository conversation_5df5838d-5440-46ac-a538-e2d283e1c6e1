// RFC-501: Admin Payment Management Routes
import { Router } from 'express';
import { authMiddleware, requireAdmin } from '../../middleware/authMiddleware';
import { PaymentManagementService } from '../../services/admin/PaymentManagementService';
import { ProducerApprovalService } from '../../services/admin/ProducerApprovalService';

const router = Router();

// Initialize services
const paymentService = new PaymentManagementService();
const approvalService = new ProducerApprovalService();

/**
 * @swagger
 * /api/admin/payments/bank-transfers/pending:
 *   get:
 *     summary: Get pending bank transfer verifications
 *     tags: [Admin Payment Management]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Pending bank transfers retrieved successfully
 */
router.get('/payments/bank-transfers/pending', authMiddleware, requireAdmin, async (req, res) => {
  try {
    const pendingTransfers = await paymentService.getPendingBankTransferVerifications();
    res.json({
      success: true,
      data: pendingTransfers
    });
  } catch (error) {
    console.error('Error fetching pending bank transfers:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch pending bank transfers'
    });
  } finally {
    await paymentService.disconnect();
  }
});

/**
 * @swagger
 * /api/admin/payments/bank-transfers/{receiptId}/verify:
 *   post:
 *     summary: Verify or reject bank transfer receipt
 *     tags: [Admin Payment Management]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: receiptId
 *         required: true
 *         schema:
 *           type: string
 *         description: Receipt ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               verified:
 *                 type: boolean
 *               reason:
 *                 type: string
 *             required:
 *               - verified
 *     responses:
 *       200:
 *         description: Bank transfer verification completed
 */
router.post('/payments/bank-transfers/:receiptId/verify', authMiddleware, requireAdmin, async (req, res) => {
  try {
    const { verified, reason } = req.body;
    const adminId = req.user?.id;

    if (!adminId) {
      return res.status(401).json({
        success: false,
        message: 'Admin ID not found'
      });
    }

    await paymentService.verifyBankTransferReceipt(req.params.receiptId, adminId, verified, reason);

    res.json({
      success: true,
      message: verified ? 'Bank transfer verified successfully' : 'Bank transfer rejected'
    });
  } catch (error) {
    console.error('Error verifying bank transfer:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to verify bank transfer'
    });
  } finally {
    await paymentService.disconnect();
  }
});

/**
 * @swagger
 * /api/admin/payments/escrow:
 *   get:
 *     summary: Get active escrow accounts
 *     tags: [Admin Payment Management]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Active escrow accounts retrieved successfully
 */
router.get('/payments/escrow', authMiddleware, requireAdmin, async (req, res) => {
  try {
    const escrowAccounts = await paymentService.getActiveEscrowAccounts();
    res.json({
      success: true,
      data: escrowAccounts
    });
  } catch (error) {
    console.error('Error fetching escrow accounts:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch escrow accounts'
    });
  } finally {
    await paymentService.disconnect();
  }
});

/**
 * @swagger
 * /api/admin/payments/escrow/{escrowId}/release:
 *   post:
 *     summary: Release escrow payment to producer
 *     tags: [Admin Payment Management]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: escrowId
 *         required: true
 *         schema:
 *           type: string
 *         description: Escrow ID
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               reason:
 *                 type: string
 *     responses:
 *       200:
 *         description: Escrow payment released successfully
 */
router.post('/payments/escrow/:escrowId/release', authMiddleware, requireAdmin, async (req, res) => {
  try {
    const { reason } = req.body;
    const adminId = req.user?.id;

    if (!adminId) {
      return res.status(401).json({
        success: false,
        message: 'Admin ID not found'
      });
    }

    await paymentService.releaseEscrow(req.params.escrowId, adminId, reason);

    res.json({
      success: true,
      message: 'Escrow payment released successfully'
    });
  } catch (error) {
    console.error('Error releasing escrow:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to release escrow payment'
    });
  } finally {
    await paymentService.disconnect();
  }
});

/**
 * @swagger
 * /api/admin/payments/commission-tracking:
 *   get:
 *     summary: Get commission tracking data
 *     tags: [Admin Payment Management]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Commission tracking data retrieved successfully
 */
router.get('/payments/commission-tracking', authMiddleware, requireAdmin, async (req, res) => {
  try {
    const commissionData = await paymentService.getCommissionTracking();
    res.json({
      success: true,
      data: commissionData
    });
  } catch (error) {
    console.error('Error fetching commission tracking:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch commission tracking data'
    });
  } finally {
    await paymentService.disconnect();
  }
});

/**
 * @swagger
 * /api/admin/payments/analytics:
 *   get:
 *     summary: Get payment analytics
 *     tags: [Admin Payment Management]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Payment analytics retrieved successfully
 */
router.get('/payments/analytics', authMiddleware, requireAdmin, async (req, res) => {
  try {
    const analytics = await paymentService.getPaymentAnalytics();
    res.json({
      success: true,
      data: analytics
    });
  } catch (error) {
    console.error('Error fetching payment analytics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch payment analytics'
    });
  } finally {
    await paymentService.disconnect();
  }
});

/**
 * @swagger
 * /api/admin/producers/pending:
 *   get:
 *     summary: Get pending producer approvals
 *     tags: [Admin Producer Management]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Pending producer approvals retrieved successfully
 */
router.get('/producers/pending', authMiddleware, requireAdmin, async (req, res) => {
  try {
    const pendingApprovals = await approvalService.getPendingApprovals();
    res.json({
      success: true,
      data: pendingApprovals
    });
  } catch (error) {
    console.error('Error fetching pending approvals:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch pending approvals'
    });
  } finally {
    await approvalService.disconnect();
  }
});

/**
 * @swagger
 * /api/admin/producers/{applicationId}/approve:
 *   post:
 *     summary: Approve producer application
 *     tags: [Admin Producer Management]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: applicationId
 *         required: true
 *         schema:
 *           type: string
 *         description: Application ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               notes:
 *                 type: string
 *               conditions:
 *                 type: array
 *                 items:
 *                   type: string
 *     responses:
 *       200:
 *         description: Producer approved successfully
 */
router.post('/producers/:applicationId/approve', authMiddleware, requireAdmin, async (req, res) => {
  try {
    const { notes, conditions } = req.body;
    const adminId = req.user?.id;

    if (!adminId) {
      return res.status(401).json({
        success: false,
        message: 'Admin ID not found'
      });
    }

    await approvalService.approveProducer(req.params.applicationId, {
      approved: true,
      notes,
      conditions,
      approvedBy: adminId
    });

    res.json({
      success: true,
      message: 'Producer approved successfully'
    });
  } catch (error) {
    console.error('Error approving producer:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to approve producer'
    });
  } finally {
    await approvalService.disconnect();
  }
});

/**
 * @swagger
 * /api/admin/producers/{applicationId}/reject:
 *   post:
 *     summary: Reject producer application
 *     tags: [Admin Producer Management]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: applicationId
 *         required: true
 *         schema:
 *           type: string
 *         description: Application ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               reason:
 *                 type: string
 *             required:
 *               - reason
 *     responses:
 *       200:
 *         description: Producer application rejected
 */
router.post('/producers/:applicationId/reject', authMiddleware, requireAdmin, async (req, res) => {
  try {
    const { reason } = req.body;
    const adminId = req.user?.id;

    if (!adminId) {
      return res.status(401).json({
        success: false,
        message: 'Admin ID not found'
      });
    }

    await approvalService.rejectApplication(req.params.applicationId, reason, adminId);

    res.json({
      success: true,
      message: 'Producer application rejected'
    });
  } catch (error) {
    console.error('Error rejecting producer application:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to reject producer application'
    });
  } finally {
    await approvalService.disconnect();
  }
});

export default router;
